import React from 'react'

export const App: React.FC = () => {
  return (
    <div style={{ fontFamily: 'sans-serif', padding: 16 }}>
      <h1><PERSON><PERSON> Editor (MVP)</h1>
      <section>
        <h2>Skill Graph</h2>
        <p>React Flow UI placeholder. Wizards and validation squiggles TBD.</p>
      </section>
      <section>
        <h2>Preview</h2>
        <p>Three.js sandbox placeholder (particles/keyframes) with FPS/budget overlay.</p>
      </section>
      <section>
        <h2>Items & RPG Designers</h2>
        <p>Minimal placeholder forms for Items and Class Trees to satisfy MVP.</p>
      </section>
      <section>
        <h2>Importers Equivalence View</h2>
        <p>Stub table comparing Mythic/MMOItems/MMOCore to Orvyn graphs/stats.</p>
      </section>
      <section>
        <h2>Dev Bridge</h2>
        <DevPing />
      </section>
    </div>
  )
}

const DevPing: React.FC = () => {
  const [status, setStatus] = React.useState<string>('idle')
  const ping = async () => {
    try {
      const res = await fetch('http://127.0.0.1:8765/api/ping')
      const j = await res.json()
      setStatus(`ok: ${j.ok}`)
    } catch (e: any) {
      setStatus('unavailable')
    }
  }
  return (
    <div>
      <button onClick={ping}>Ping Dev Server</button>
      <span style={{ marginLeft: 8 }}>Status: {status}</span>
    </div>
  )
}
