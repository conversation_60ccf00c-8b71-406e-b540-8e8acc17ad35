name: OrvynMMO
main: orvynmmo.OrvynMMOPlugin
version: 0.1.0-SNAPSHOT
api-version: "1.20"
folia-supported: true
load: POSTWORLD
commands:
  orvyn:
    description: OrvynMMO admin command
    usage: "/orvyn <reload|profile|test|import>"
    permission: orvyn.admin.*
permissions:
  orvyn.admin.*:
    description: All Orvyn admin permissions
    default: op
    children:
      orvyn.profile: true
      orvyn.reload: true
      orvyn.test: true
      orvyn.import: true
  orvyn.profile:
    description: Use profiling HUD and exports
    default: op
  orvyn.reload:
    description: Reload packs and runtime
    default: op
  orvyn.test:
    description: Run headless tests
    default: op
  orvyn.import:
    description: Import external content
    default: op