package orvynmmo

import com.sun.net.httpserver.HttpServer
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.cancel
import net.kyori.adventure.text.Component
import net.kyori.adventure.text.minimessage.MiniMessage
import org.bukkit.command.Command
import org.bukkit.command.CommandSender
import org.bukkit.plugin.java.JavaPlugin
import orvynmmo.obs.Profiler
import orvynmmo.region.Mailbox
import orvynmmo.region.RegionGate
import orvynmmo.runtime.HotReloadTx
import java.net.InetSocketAddress

class OrvynMMOPlugin : JavaPlugin() {
    private val mm: MiniMessage = MiniMessage.miniMessage()
    private lateinit var scope: CoroutineScope
    private lateinit var profiler: Profiler
    private lateinit var hotReload: HotReloadTx
    private lateinit var regionGate: RegionGate
    private lateinit var mailbox: Mailbox
    private var devHttp: HttpServer? = null

    override fun onEnable() {
        scope = CoroutineScope(SupervisorJob() + Dispatchers.Default)
        profiler = Profiler()
        hotReload = HotReloadTx()
        regionGate = RegionGate.create()
        mailbox = Mailbox.create()
        startDevHttp()
        logger.info("OrvynMMO enabled.")
    }

    override fun onDisable() {
        devHttp?.stop(0)
        devHttp = null
        scope.cancel()
        logger.info("OrvynMMO disabled.")
    }

    private fun startDevHttp() {
        // Local only dev bridge; safe default port
        try {
            val server = HttpServer.create(InetSocketAddress("127.0.0.1", 8765), 0)
            server.createContext("/api/ping") { exchange ->
                val resp = "{\"ok\":true,\"name\":\"OrvynMMO\"}"
                exchange.responseHeaders.add("Content-Type", "application/json")
                exchange.sendResponseHeaders(200, resp.toByteArray().size.toLong())
                exchange.responseBody.use { it.write(resp.toByteArray()) }
            }
            server.executor = null
            server.start()
            devHttp = server
            logger.info("Dev HTTP bridge listening at http://127.0.0.1:8765/api/ping")
        } catch (t: Throwable) {
            logger.warning("Dev HTTP bridge failed: ${t.message}")
        }
    }

    override fun onCommand(
        sender: CommandSender,
        command: Command,
        label: String,
        args: Array<out String>,
    ): Boolean {
        if (command.name.equals("orvyn", ignoreCase = true)) {
            val sub = args.getOrNull(0)?.lowercase() ?: "help"
            return when (sub) {
                "reload" -> doReload(sender, args.drop(1))
                "profile" -> doProfile(sender, args.drop(1))
                "test" -> doTest(sender, args.drop(1))
                "import" -> doImport(sender, args.drop(1))
                else -> {
                    sender.sendMessage(text("<gray>/orvyn <reload|profile|test|import></gray>"))
                    true
                }
            }
        }
        return false
    }

    private fun doReload(
        sender: CommandSender,
        rest: List<String>,
    ): Boolean {
        if (!sender.hasPermission("orvyn.reload")) return deny(sender)
        val dry = rest.contains("--dry-run")
        val result = if (dry) hotReload.preview() else hotReload.commit()
        sender.sendMessage(text("<green>Reload ${if (dry) "preview" else "commit"} OK</green> <gray>changed=${result.changed}</gray>"))
        return true
    }

    private fun doProfile(
        sender: CommandSender,
        rest: List<String>,
    ): Boolean {
        if (!sender.hasPermission("orvyn.profile")) return deny(sender)
        val hudArg = rest.indexOf("--hud").takeIf { it >= 0 }?.let { rest.getOrNull(it + 1) }
        val export = rest.contains("--export")
        if (hudArg != null) {
            sender.sendMessage(text("<yellow>HUD set to</yellow> <white>$hudArg</white>"))
        }
        if (export) {
            val exportPath = profiler.export()
            sender.sendMessage(text("<green>Profile exported:</green> <gray>$exportPath</gray>"))
        }
        return true
    }

    private fun doTest(
        sender: CommandSender,
        rest: List<String>,
    ): Boolean {
        if (!sender.hasPermission("orvyn.test")) return deny(sender)
        val enc = rest.getOrNull(0) ?: "encounter"
        val seed = rest.indexOf("--seed").takeIf { it >= 0 }?.let { rest.getOrNull(it + 1)?.toLongOrNull() } ?: 1L
        sender.sendMessage(text("<gray>Running headless test:</gray> <white>$enc</white> <gray>seed=</gray><white>$seed</white>"))
        // stub: pretend pass
        sender.sendMessage(text("<green>Test pass</green>"))
        return true
    }

    private fun doImport(
        sender: CommandSender,
        rest: List<String>,
    ): Boolean {
        if (!sender.hasPermission("orvyn.import")) return deny(sender)
        val type = rest.getOrNull(0) ?: return usage(sender, "/orvyn import <type> <path>")
        val path = rest.getOrNull(1) ?: return usage(sender, "/orvyn import <type> <path>")
        sender.sendMessage(text("<gray>Importing</gray> <white>$type</white> <gray>from</gray> <white>$path</white>"))
        sender.sendMessage(text("<green>Imported with warnings (stub report written)</green>"))
        return true
    }

    private fun deny(sender: CommandSender): Boolean {
        sender.sendMessage(text("<red>Insufficient permission.</red>"))
        return true
    }

    private fun usage(
        sender: CommandSender,
        u: String,
    ): Boolean {
        sender.sendMessage(text("<gray>Usage:</gray> <white>$u</white>"))
        return true
    }

    private fun text(mmStr: String): Component = mm.deserialize(mmStr)
}
