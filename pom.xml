<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>orvynmmo</groupId>
    <artifactId>orvynmmo</artifactId>
    <version>0.1.0-SNAPSHOT</version>
    <packaging>pom</packaging>
    <name>OrvynMMO Monorepo</name>
    <description>Aggregator POM for OrvynMMO (plugin/editor/cli/schemas/examples)</description>

    <modules>
        <module>plugin</module>
    </modules>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <maven.compiler.release>17</maven.compiler.release>
        <kotlin.version>1.9.25</kotlin.version>
        <kotlin.coroutines.version>1.8.1</kotlin.coroutines.version>
        <paper.api.version>1.21-R0.1-SNAPSHOT</paper.api.version>
        <adventure.version>4.17.0</adventure.version>
    </properties>

    <build>
        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>org.apache.maven.plugins</groupId>
                    <artifactId>maven-toolchain-plugin</artifactId>
                    <version>3.2.0</version>
                    <executions>
                        <execution>
                            <goals>
                                <goal>toolchain</goal>
                            </goals>
                        </execution>
                    </executions>
                    <configuration>
                        <toolchains>
                            <jdk>
                                <version>17</version>
                                <vendor>temurin</vendor>
                            </jdk>
                        </toolchains>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>
</project>