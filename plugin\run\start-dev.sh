#!/usr/bin/env bash
set -euo pipefail
cd "$(dirname "$0")"
PLUGINS_DIR="plugins"
JAR_SRC="../target/OrvynMMO-0.1.0-SNAPSHOT-shaded.jar"
JAR_DST="$PLUGINS_DIR/OrvynMMO.jar"

mkdir -p "$PLUGINS_DIR"
if [ -f "$JAR_SRC" ]; then
  cp "$JAR_SRC" "$JAR_DST"
  echo "Copied $JAR_SRC -> $JAR_DST"
else
  echo "Shaded jar not found at $JAR_SRC. Build with: mvn -q -B -DskipTests clean package"
fi

if [ ! -f "paper.jar" ]; then
  echo "Missing paper.jar in run/. Place a Paper or Folia jar named paper.jar here."
  exit 1
fi

JAVA_OPTS="-Dpaper.disableChannelWarning=true -Xms2G -Xmx4G"
exec java $JAVA_OPTS -jar paper.jar nogui
