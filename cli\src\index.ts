#!/usr/bin/env node
import { Command } from 'commander'
import Ajv from 'ajv'

const program = new Command()
program.name('orvyn').description('OrvynMMO CLI')

program
  .command('pack')
  .description('Pack operations')
  .argument('<action>', 'init|validate|compile|lock|sign|publish|install|update')
  .argument('[path]')
  .action(async (action: string, path?: string) => {
    console.log(`[pack] ${action} ${path ?? ''}`.trim())
  })

program
  .command('import')
  .description('Import external packs')
  .argument('<type>', 'mythic|mmoitems|mmocore')
  .argument('<src>')
  .action(async (type: string, src: string) => {
    console.log(`[import] ${type} from ${src}`)
    console.log('conversion-report.md written (stub)')
  })

program
  .command('test')
  .description('Headless tests')
  .argument('run')
  .argument('<pack>')
  .option('--scenario <name>')
  .option('--seed <n>')
  .action(async (_run: string, pack: string, opts: any) => {
    console.log(`[test run] pack=${pack} scenario=${opts.scenario ?? 'default'} seed=${opts.seed ?? '1'}`)
    console.log('PASS (stub)')
  })

program
  .command('doctor')
  .description('Environment scan')
  .action(async () => {
    console.log('Checking Folia availability, adapters... (stub)')
    console.log('ModelEngine: MISSING (null adapter fallback)')
    console.log('Server: clean (stub)')
  })

program.parse()
