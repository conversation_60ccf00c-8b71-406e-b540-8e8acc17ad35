package orvynmmo.items

sealed interface AbilityTrigger { object OnHit : AbilityTrigger; object OnUse : AbilityTrigger }

data class Stat(val name: String, val value: Double)

data class ItemDef(
    val id: String,
    val displayName: String,
    val stats: List<Stat> = emptyList(),
)

data class Ability(
    val id: String,
    val trigger: AbilityTrigger,
)

class ItemRuntime {
    fun computePower(item: ItemDef): Double = item.stats.sumOf { it.value }
}
