package orvynmmo

import orvynmmo.items.ItemDef
import orvynmmo.items.ItemRuntime
import orvynmmo.items.Stat
import orvynmmo.rpg.ClassNode
import orvynmmo.rpg.ClassTree
import orvynmmo.rpg.RpgRuntime
import kotlin.test.Test
import kotlin.test.assertEquals

class PluginSmokeTest {
    @Test
    fun itemPower() {
        val power = ItemRuntime().computePower(ItemDef("arc", "Arc Javelin", listOf(Stat("atk", 5.0), Stat("agi", 2.0))))
        assertEquals(7.0, power)
    }

    @Test
    fun rpgCost() {
        val cost = RpgRuntime().totalCost(ClassTree("arcanist", "Arcanist", listOf(ClassNode("n1", "Zap", 1), ClassNode("n2", "Shock", 2))))
        assertEquals(3, cost)
    }
}
