<?xml version="1.0" encoding="UTF-8"?>
<testsuite xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="https://maven.apache.org/surefire/maven-surefire-plugin/xsd/surefire-test-report-3.0.xsd" version="3.0" name="orvynmmo.PluginSmokeTest" time="0.04" tests="2" errors="0" skipped="0" failures="0">
  <properties>
    <property name="java.specification.version" value="21"/>
    <property name="sun.cpu.isalist" value="amd64"/>
    <property name="sun.jnu.encoding" value="Cp1252"/>
    <property name="java.class.path" value="C:\Users\<USER>\Downloads\OrvynMMO CORE PLUGINS\plugin\target\test-classes;C:\Users\<USER>\Downloads\OrvynMMO CORE PLUGINS\plugin\target\classes;C:\Users\<USER>\.m2\repository\io\papermc\paper\paper-api\1.21-R0.1-SNAPSHOT\paper-api-1.21-R0.1-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\mojang\brigadier\1.2.9\brigadier-1.2.9.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\32.1.2-jre\guava-32.1.2-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.18.0\error_prone_annotations-2.18.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\net\md-5\bungeecord-chat\1.20-R0.2-deprecated+build.18\bungeecord-chat-1.20-R0.2-deprecated+build.18.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\joml\joml\1.10.5\joml-1.10.5.jar;C:\Users\<USER>\.m2\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;C:\Users\<USER>\.m2\repository\it\unimi\dsi\fastutil\8.5.6\fastutil-8.5.6.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.1\log4j-api-2.17.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-resolver-provider\3.9.6\maven-resolver-provider-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\3.9.6\maven-model-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model-builder\3.9.6\maven-model-builder-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.26\plexus-interpolation-1.26.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\3.9.6\maven-artifact-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-builder-support\3.9.6\maven-builder-support-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\eclipse\sisu\org.eclipse.sisu.inject\0.9.0.M2\org.eclipse.sisu.inject-0.9.0.M2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\3.9.6\maven-repository-metadata-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-api\1.9.18\maven-resolver-api-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-spi\1.9.18\maven-resolver-spi-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-util\1.9.18\maven-resolver-util-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-impl\1.9.18\maven-resolver-impl-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-named-locks\1.9.18\maven-resolver-named-locks-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\3.5.1\plexus-utils-3.5.1.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson\4.17.0\adventure-text-serializer-gson-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-json\4.17.0\adventure-text-serializer-json-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\option\1.0.0\option-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\auto\service\auto-service-annotations\1.1.1\auto-service-annotations-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-legacy\4.17.0\adventure-text-serializer-legacy-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-plain\4.17.0\adventure-text-serializer-plain-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-logger-slf4j\4.17.0\adventure-text-logger-slf4j-4.17.0.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.7\asm-9.7.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-commons\9.7\asm-commons-9.7.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-tree\9.7\asm-tree-9.7.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.25\kotlin-stdlib-jdk8-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.25\kotlin-stdlib-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.25\kotlin-stdlib-jdk7-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core\1.8.1\kotlinx-coroutines-core-1.8.1.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core-jvm\1.8.1\kotlinx-coroutines-core-jvm-1.8.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-api\4.17.0\adventure-api-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-key\4.17.0\adventure-key-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-api\1.3.0\examination-api-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-string\1.3.0\examination-string-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\24.1.0\annotations-24.1.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-minimessage\4.17.0\adventure-text-minimessage-4.17.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-test\1.9.25\kotlin-test-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-test-junit5\1.9.25\kotlin-test-junit5-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;"/>
    <property name="java.vm.vendor" value="Microsoft"/>
    <property name="sun.arch.data.model" value="64"/>
    <property name="user.variant" value=""/>
    <property name="java.vendor.url" value="https://www.microsoft.com"/>
    <property name="user.timezone" value="Australia/Sydney"/>
    <property name="os.name" value="Windows 11"/>
    <property name="java.vm.specification.version" value="21"/>
    <property name="sun.java.launcher" value="SUN_STANDARD"/>
    <property name="user.country" value="AU"/>
    <property name="sun.boot.library.path" value="C:\Program Files\Microsoft\jdk-********-hotspot\bin"/>
    <property name="sun.java.command" value="C:\Users\<USER>\AppData\Local\Temp\surefire13216771371436990403\surefirebooter-20251004175741175_3.jar C:\Users\<USER>\AppData\Local\Temp\surefire13216771371436990403 2025-10-04T17-57-37_759-jvmRun1 surefire-20251004175741175_1tmp surefire_0-20251004175741175_2tmp"/>
    <property name="jdk.debug" value="release"/>
    <property name="surefire.test.class.path" value="C:\Users\<USER>\Downloads\OrvynMMO CORE PLUGINS\plugin\target\test-classes;C:\Users\<USER>\Downloads\OrvynMMO CORE PLUGINS\plugin\target\classes;C:\Users\<USER>\.m2\repository\io\papermc\paper\paper-api\1.21-R0.1-SNAPSHOT\paper-api-1.21-R0.1-SNAPSHOT.jar;C:\Users\<USER>\.m2\repository\com\mojang\brigadier\1.2.9\brigadier-1.2.9.jar;C:\Users\<USER>\.m2\repository\com\google\guava\guava\32.1.2-jre\guava-32.1.2-jre.jar;C:\Users\<USER>\.m2\repository\com\google\guava\failureaccess\1.0.1\failureaccess-1.0.1.jar;C:\Users\<USER>\.m2\repository\com\google\guava\listenablefuture\9999.0-empty-to-avoid-conflict-with-guava\listenablefuture-9999.0-empty-to-avoid-conflict-with-guava.jar;C:\Users\<USER>\.m2\repository\com\google\code\findbugs\jsr305\3.0.2\jsr305-3.0.2.jar;C:\Users\<USER>\.m2\repository\com\google\errorprone\error_prone_annotations\2.18.0\error_prone_annotations-2.18.0.jar;C:\Users\<USER>\.m2\repository\com\google\j2objc\j2objc-annotations\2.8\j2objc-annotations-2.8.jar;C:\Users\<USER>\.m2\repository\com\google\code\gson\gson\2.10.1\gson-2.10.1.jar;C:\Users\<USER>\.m2\repository\net\md-5\bungeecord-chat\1.20-R0.2-deprecated+build.18\bungeecord-chat-1.20-R0.2-deprecated+build.18.jar;C:\Users\<USER>\.m2\repository\org\yaml\snakeyaml\2.2\snakeyaml-2.2.jar;C:\Users\<USER>\.m2\repository\org\joml\joml\1.10.5\joml-1.10.5.jar;C:\Users\<USER>\.m2\repository\com\googlecode\json-simple\json-simple\1.1.1\json-simple-1.1.1.jar;C:\Users\<USER>\.m2\repository\it\unimi\dsi\fastutil\8.5.6\fastutil-8.5.6.jar;C:\Users\<USER>\.m2\repository\org\apache\logging\log4j\log4j-api\2.17.1\log4j-api-2.17.1.jar;C:\Users\<USER>\.m2\repository\org\slf4j\slf4j-api\2.0.9\slf4j-api-2.0.9.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-resolver-provider\3.9.6\maven-resolver-provider-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model\3.9.6\maven-model-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-model-builder\3.9.6\maven-model-builder-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-interpolation\1.26\plexus-interpolation-1.26.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-artifact\3.9.6\maven-artifact-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\commons\commons-lang3\3.12.0\commons-lang3-3.12.0.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-builder-support\3.9.6\maven-builder-support-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\eclipse\sisu\org.eclipse.sisu.inject\0.9.0.M2\org.eclipse.sisu.inject-0.9.0.M2.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\maven-repository-metadata\3.9.6\maven-repository-metadata-3.9.6.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-api\1.9.18\maven-resolver-api-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-spi\1.9.18\maven-resolver-spi-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-util\1.9.18\maven-resolver-util-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-impl\1.9.18\maven-resolver-impl-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\apache\maven\resolver\maven-resolver-named-locks\1.9.18\maven-resolver-named-locks-1.9.18.jar;C:\Users\<USER>\.m2\repository\org\codehaus\plexus\plexus-utils\3.5.1\plexus-utils-3.5.1.jar;C:\Users\<USER>\.m2\repository\javax\inject\javax.inject\1\javax.inject-1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-gson\4.17.0\adventure-text-serializer-gson-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-json\4.17.0\adventure-text-serializer-json-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\option\1.0.0\option-1.0.0.jar;C:\Users\<USER>\.m2\repository\com\google\auto\service\auto-service-annotations\1.1.1\auto-service-annotations-1.1.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-legacy\4.17.0\adventure-text-serializer-legacy-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-serializer-plain\4.17.0\adventure-text-serializer-plain-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-logger-slf4j\4.17.0\adventure-text-logger-slf4j-4.17.0.jar;C:\Users\<USER>\.m2\repository\org\checkerframework\checker-qual\3.33.0\checker-qual-3.33.0.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm\9.7\asm-9.7.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-commons\9.7\asm-commons-9.7.jar;C:\Users\<USER>\.m2\repository\org\ow2\asm\asm-tree\9.7\asm-tree-9.7.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk8\1.9.25\kotlin-stdlib-jdk8-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib\1.9.25\kotlin-stdlib-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-stdlib-jdk7\1.9.25\kotlin-stdlib-jdk7-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core\1.8.1\kotlinx-coroutines-core-1.8.1.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlinx\kotlinx-coroutines-core-jvm\1.8.1\kotlinx-coroutines-core-jvm-1.8.1.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-api\4.17.0\adventure-api-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-key\4.17.0\adventure-key-4.17.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-api\1.3.0\examination-api-1.3.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\examination-string\1.3.0\examination-string-1.3.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\annotations\24.1.0\annotations-24.1.0.jar;C:\Users\<USER>\.m2\repository\net\kyori\adventure-text-minimessage\4.17.0\adventure-text-minimessage-4.17.0.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-test\1.9.25\kotlin-test-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\jetbrains\kotlin\kotlin-test-junit5\1.9.25\kotlin-test-junit5-1.9.25.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-api\5.6.3\junit-jupiter-api-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\apiguardian\apiguardian-api\1.1.0\apiguardian-api-1.1.0.jar;C:\Users\<USER>\.m2\repository\org\opentest4j\opentest4j\1.2.0\opentest4j-1.2.0.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-commons\1.6.3\junit-platform-commons-1.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\jupiter\junit-jupiter-engine\5.6.3\junit-jupiter-engine-5.6.3.jar;C:\Users\<USER>\.m2\repository\org\junit\platform\junit-platform-engine\1.6.3\junit-platform-engine-1.6.3.jar;"/>
    <property name="sun.cpu.endian" value="little"/>
    <property name="user.home" value="C:\Users\<USER>\Program Files\Microsoft\jdk-********-hotspot"/>
    <property name="file.separator" value="\"/>
    <property name="basedir" value="C:\Users\<USER>\Downloads\OrvynMMO CORE PLUGINS\plugin"/>
    <property name="java.vm.compressedOopsMode" value="Zero based"/>
    <property name="line.separator" value="&#10;"/>
    <property name="java.vm.specification.vendor" value="Oracle Corporation"/>
    <property name="java.specification.name" value="Java Platform API Specification"/>
    <property name="surefire.real.class.path" value="C:\Users\<USER>\AppData\Local\Temp\surefire13216771371436990403\surefirebooter-20251004175741175_3.jar"/>
    <property name="user.script" value=""/>
    <property name="sun.management.compiler" value="HotSpot 64-Bit Tiered Compilers"/>
    <property name="java.runtime.version" value="21.0.8+9-LTS"/>
    <property name="user.name" value="grady"/>
    <property name="stdout.encoding" value="Cp1252"/>
    <property name="path.separator" value=";"/>
    <property name="os.version" value="10.0"/>
    <property name="java.runtime.name" value="OpenJDK Runtime Environment"/>
    <property name="file.encoding" value="UTF-8"/>
    <property name="java.vm.name" value="OpenJDK 64-Bit Server VM"/>
    <property name="java.vendor.version" value="Microsoft-11933218"/>
    <property name="localRepository" value="C:\Users\<USER>\.m2\repository"/>
    <property name="java.vendor.url.bug" value="https://github.com/microsoft/openjdk/issues"/>
    <property name="java.io.tmpdir" value="C:\Users\<USER>\AppData\Local\Temp\"/>
    <property name="java.version" value="21.0.8"/>
    <property name="user.dir" value="C:\Users\<USER>\Downloads\OrvynMMO CORE PLUGINS\plugin"/>
    <property name="os.arch" value="amd64"/>
    <property name="java.vm.specification.name" value="Java Virtual Machine Specification"/>
    <property name="sun.os.patch.level" value=""/>
    <property name="native.encoding" value="Cp1252"/>
    <property name="java.library.path" value="C:\Program Files\Microsoft\jdk-********-hotspot\bin;C:\WINDOWS\Sun\Java\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\Program Files\Microsoft\jdk-********-hotspot\bin;C:\Program Files\Common Files\Oracle\Java\javapath;C:\Program Files (x86)\Common Files\Oracle\Java\java8path;C:\Program Files (x86)\Common Files\Oracle\Java\javapath;C:\Program Files\Eclipse Adoptium\jre-********-hotspot\bin;C:\Program Files\Eclipse Adoptium\jdk-********-hotspot\bin;C:\WINDOWS\system32;C:\WINDOWS;C:\WINDOWS\System32\Wbem;C:\WINDOWS\System32\WindowsPowerShell\v1.0\;C:\WINDOWS\System32\OpenSSH\;C:\Program Files\NVIDIA Corporation\NVIDIA App\NvDLISR;C:\Program Files (x86)\NVIDIA Corporation\PhysX\Common;C:\Program Files\Microsoft SQL Server\120\Tools\Binn\;C:\Program Files\Git\cmd;C:\Program Files\nodejs\;C:\Program Files\Docker\Docker\resources\bin;C:\Program Files\playit_gg\bin\;C:\Program Files\Amazon\AWSCLIV2\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\Scripts\;C:\Users\<USER>\AppData\Local\Programs\Python\Python313\;C:\Users\<USER>\AppData\Local\Programs\Python\Launcher\;C:\Users\<USER>\AppData\Local\Microsoft\WindowsApps;C:\Users\<USER>\AppData\Local\Programs\Microsoft VS Code\bin;C:\Users\<USER>\AppData\Local\GitHubDesktop\bin;C:\Users\<USER>\AppData\Roaming\npm;C:\Users\<USER>\AppData\Local\Programs\Ollama;c:\Users\<USER>\AppData\Roaming\Code\User\globalStorage\github.copilot-chat\debugCommand;."/>
    <property name="java.vm.info" value="mixed mode, sharing"/>
    <property name="stderr.encoding" value="Cp1252"/>
    <property name="java.vendor" value="Microsoft"/>
    <property name="java.vm.version" value="21.0.8+9-LTS"/>
    <property name="sun.io.unicode.encoding" value="UnicodeLittle"/>
    <property name="java.class.version" value="65.0"/>
  </properties>
  <testcase name="rpgCost" classname="orvynmmo.PluginSmokeTest" time="0.026"/>
  <testcase name="itemPower" classname="orvynmmo.PluginSmokeTest" time="0.002"/>
</testsuite>