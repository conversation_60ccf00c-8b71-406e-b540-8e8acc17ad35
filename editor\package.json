{"name": "@orvynmmo/editor", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "typecheck": "tsc -b --noEmit"}, "devDependencies": {"@types/react": "18.3.7", "@types/react-dom": "18.3.0", "eslint": "9.12.0", "eslint-config-prettier": "9.1.0", "eslint-plugin-react": "7.37.2", "prettier": "3.3.3", "tailwindcss": "3.4.12", "typescript": "5.4.5", "vite": "5.4.8", "@vitejs/plugin-react": "4.3.3"}, "dependencies": {"react": "18.3.1", "react-dom": "18.3.1"}}