package orvynmmo.region

/**
 * Cross-region message bus. Minimal stub queues runnables; real impl would use
 * Folia's region mailbox executors with backpressure policies.
 */
interface Mailbox {
    fun send(
        targetRegionId: Int,
        runnable: () -> Unit,
    )

    companion object {
        fun create(): Mailbox =
            object : Mailbox {
                override fun send(
                    targetRegionId: Int,
                    runnable: () -> Unit,
                ) {
                    runnable()
                }
            }
    }
}
