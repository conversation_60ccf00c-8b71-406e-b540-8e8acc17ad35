package orvynmmo.region

/**
 * Folia-first region execution gate. In production this would post work to the
 * correct region thread. Here we provide a minimal stub interface + factory.
 */
interface RegionGate {
    fun post(entityId: Int, runnable: () -> Unit)

    companion object {
        fun create(): RegionGate = object : RegionGate {
            override fun post(entityId: Int, runnable: () -> Unit) {
                // Minimal stub executes immediately; real impl must dispatch to region executor.
                runnable()
            }
        }
    }
}
