$ErrorActionPreference = "Stop"
Set-Location -Path $PSScriptRoot

$pluginsDir = "plugins"
$jarSrc = "..\target\OrvynMMO-0.1.0-SNAPSHOT-shaded.jar"
$jarDst = Join-Path $pluginsDir "OrvynMMO.jar"

if (-not (Test-Path $pluginsDir)) { New-Item -ItemType Directory -Path $pluginsDir | Out-Null }

if (Test-Path $jarSrc) {
  Copy-Item $jarSrc $jarDst -Force
  Write-Host "Copied $jarSrc -> $jarDst"
} else {
  Write-Host "Shaded jar not found at $jarSrc. Build with: mvn -q -B -DskipTests clean package"
}

if (-not (Test-Path "paper.jar")) {
  Write-Error "Missing paper.jar in run/. Place a Paper or Folia jar named paper.jar here."
}

$javaOpts = "-Dpaper.disableChannelWarning=true -Xms2G -Xmx4G"
& java $javaOpts -jar paper.jar nogui
