package orvynmmo.skills

sealed interface SkillNode {
    val id: String
}

sealed interface Mechanic : SkillNode

data class Damage(val amount: Double, override val id: String = "damage") : Mechanic

sealed interface Targeter : SkillNode

data class SelfTarget(override val id: String = "self") : Targeter

data class SkillGraph(
    val nodes: List<SkillNode> = emptyList(),
)

class SkillRunner {
    fun run(graph: SkillGraph): <PERSON><PERSON>an {
        // Stub: pretend to run successfully
        return graph.nodes.isNotEmpty() || graph.nodes.isEmpty()
    }
}
